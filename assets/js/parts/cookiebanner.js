// Cookie Banner JavaScript with Swup integration
var cookieBannerInitialized = false;

$(document).ready(function() {
    initCookieBanner();
});

// Initialize on Swup page transitions
$(document).on('initPage', function() {
    initCookieBanner();
});

function initCookieBanner() {
    // Remove privacy statement link if it exists
    removePrivacyStatementLink();
    
    // Set up event handlers with namespacing for Swup compatibility
    setupCookieBannerEvents();
    
    // Apply custom styling and hide unwanted elements
    customizeCookieBanner();
    
    cookieBannerInitialized = true;
}

function removePrivacyStatementLink() {
    // Remove privacy statement link as requested
    $('.cmplz-link.privacy-statement').remove();
}

function setupCookieBannerEvents() {
    // Use namespaced events to prevent duplicates with Swup
    $(document).off('click.cookiebanner', '.cmplz-btn.cmplz-accept')
               .on('click.cookiebanner', '.cmplz-btn.cmplz-accept', function(e) {
        handleAcceptCookies();
    });
    
    $(document).off('click.cookiebanner', '.cmplz-btn.cmplz-deny')
               .on('click.cookiebanner', '.cmplz-btn.cmplz-deny', function(e) {
        handleDenyCookies();
    });
    
    $(document).off('click.cookiebanner', '.cmplz-close')
               .on('click.cookiebanner', '.cmplz-close', function(e) {
        handleCloseBanner();
    });
}

function customizeCookieBanner() {
    // Wait for banner to be rendered
    setTimeout(function() {
        var $banner = $('#cmplz-cookiebanner-container');
        
        if ($banner.length > 0) {
            // Hide unwanted elements
            $banner.find('.cmplz-categories').hide();
            $banner.find('.cmplz-links.cmplz-information').hide();
            $banner.find('.cmplz-divider').hide();
            $banner.find('.cmplz-btn.cmplz-view-preferences').hide();
            $banner.find('.cmplz-btn.cmplz-save-preferences').hide();
            $banner.find('.cmplz-btn.cmplz-manage-options').hide();
            $banner.find('.cmplz-links.cmplz-documents').hide();
            
            // Ensure buttons are stacked vertically
            var $buttonsContainer = $banner.find('.cmplz-buttons');
            $buttonsContainer.css({
                'display': 'flex',
                'flex-direction': 'column',
                'gap': '12px'
            });
            
            // Ensure only Accept and Deny buttons are visible
            $buttonsContainer.find('.cmplz-btn').each(function() {
                var $btn = $(this);
                if (!$btn.hasClass('cmplz-accept') && !$btn.hasClass('cmplz-deny')) {
                    $btn.hide();
                }
            });
        }
    }, 100);
}

function handleAcceptCookies() {
    // Let Complianz handle the acceptance
    // The banner will be hidden automatically by the plugin
    console.log('Cookies accepted');
}

function handleDenyCookies() {
    // Let Complianz handle the denial
    // The banner will be hidden automatically by the plugin
    console.log('Cookies denied');
}

function handleCloseBanner() {
    // Hide the banner when close button is clicked
    $('#cmplz-cookiebanner-container').fadeOut(300);
}

// Swup hooks for cookie banner management
if (typeof pageContainerWrap !== 'undefined') {
    // Before content replacement, hide cookie banner to prevent conflicts
    pageContainerWrap.hooks.before('content:replace', () => {
        var $banner = $('#cmplz-cookiebanner-container');
        if ($banner.length > 0) {
            $banner.hide();
        }
    });
    
    // After page view, reinitialize cookie banner if needed
    pageContainerWrap.hooks.on('page:view', () => {
        setTimeout(function() {
            initCookieBanner();
        }, 200);
    });
}

// Observer to watch for dynamically added cookie banners
var cookieBannerObserver = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1) { // Element node
                    var $node = $(node);
                    if ($node.is('#cmplz-cookiebanner-container') || $node.find('#cmplz-cookiebanner-container').length > 0) {
                        setTimeout(function() {
                            customizeCookieBanner();
                        }, 50);
                    }
                }
            });
        }
    });
});

// Start observing
cookieBannerObserver.observe(document.body, {
    childList: true,
    subtree: true
});

// Clean up observer when page unloads (for Swup)
$(window).on('beforeunload', function() {
    if (cookieBannerObserver) {
        cookieBannerObserver.disconnect();
    }
});
