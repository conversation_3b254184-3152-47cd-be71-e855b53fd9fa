$(document).ready(function(){
    $(document).on("initPage", function () {
        if ($(".artistMediaBlock").length > 0) {
            initializeArtistMediaBlock();
        }
    });
});

function initializeArtistMediaBlock() {
    const $block = $('.artistMediaBlock');
    console.log('Initializing artist media block, found blocks:', $block.length);

    // Only handle actual iframe errors, not CORS/security restrictions which are normal
    $block.find('iframe').each(function() {
        const $iframe = $(this);
        const $container = $iframe.closest('.spotifyContainer, .youtubeContainer');
        const iframeSrc = $iframe.attr('src');

        console.log('Found iframe with src:', iframeSrc);
        console.log('Container classes:', $container.attr('class'));

        // Log when iframe starts loading
        $iframe.on('load', function() {
            console.log('Iframe loaded successfully:', iframeSrc);
        });

        // Only listen for actual iframe errors (network issues, 404s, etc.)
        $iframe.on('error', function() {
            // Only show fallback for actual loading errors, not security restrictions
            console.log('Iframe failed to load due to network or server error:', iframeSrc);
            handleIframeError($iframe, $container);
        });

        // Remove the timeout check that was causing false positives
        // YouTube and Spotify iframes work fine even when we can't access their content due to CORS
    });
}

function handleIframeError($iframe, $container) {
    // Only show fallback for actual network/server errors, not security restrictions
    console.log('Handling iframe error - showing fallback link');
    console.log('Container type:', $container.attr('class'));
    console.log('Iframe src:', $iframe.attr('src'));

    // For Spotify containers, show a fallback
    if ($container.hasClass('spotifyContainer')) {
        const spotifyUrl = $iframe.attr('src');
        console.log('Processing Spotify fallback for URL:', spotifyUrl);

        if (spotifyUrl) {
            // Convert embed URL back to regular Spotify URL
            const regularUrl = spotifyUrl
                .replace('open.spotify.com/embed/', 'open.spotify.com/')
                .split('?')[0]; // Remove query parameters

            console.log('Converted Spotify URL to:', regularUrl);

            const fallbackHtml = `
                <div class="spotify-fallback">
                    <div class="fallback-message">
                        <p>Unable to load Spotify playlist</p>
                        <p><small>Click to open in the Spotify app</small></p>
                        <a href="${regularUrl}" target="_blank" class="button">
                            <span class="innerText">Open on Spotify</span>
                            <span class="arrows">
                                <i class="icon-arrow-right-up"></i>
                                <i class="icon-arrow-right-up"></i>
                            </span>
                        </a>
                    </div>
                </div>
            `;

            $container.html(fallbackHtml);
            console.log('Spotify fallback HTML inserted');
        }
    }

    // For YouTube containers, show a fallback
    if ($container.hasClass('youtubeContainer')) {
        const youtubeUrl = $iframe.attr('src');
        console.log('Processing YouTube fallback for URL:', youtubeUrl);

        if (youtubeUrl) {
            // Extract video ID and create regular YouTube URL
            const videoIdMatch = youtubeUrl.match(/embed\/([^?]+)/);
            console.log('Video ID match result:', videoIdMatch);

            if (videoIdMatch) {
                const videoId = videoIdMatch[1];
                const regularUrl = `https://www.youtube.com/watch?v=${videoId}`;
                console.log('Converted YouTube URL to:', regularUrl);

                const fallbackHtml = `
                    <div class="youtube-fallback">
                        <div class="fallback-message">
                            <p>Unable to load YouTube video</p>
                            <p><small>Click to open in YouTube</small></p>
                            <a href="${regularUrl}" target="_blank" class="button">
                                <span class="innerText">Open on YouTube</span>
                                <span class="arrows">
                                    <i class="icon-arrow-right-up"></i>
                                    <i class="icon-arrow-right-up"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                `;

                $container.html(fallbackHtml);
                console.log('YouTube fallback HTML inserted');
            } else {
                console.log('Could not extract video ID from YouTube URL');
            }
        }
    }
}

// Force HTTPS on any remaining HTTP links
$(document).ready(function(){
    $('iframe[src^="http://"]').each(function() {
        const $iframe = $(this);
        const src = $iframe.attr('src');
        $iframe.attr('src', src.replace('http://', 'https://'));
    });
});
