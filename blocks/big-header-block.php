<?php
  $size = 'full'; 
  $video = get_field("background_video");
  $image = get_field("background_image");
  $image1 = get_field("image_1");
  $image2 = get_field("image_2");
  $youtube_link = get_field("youtube_link");
  ?>
<section class="bigHeaderBlock whiteBackground" data-show-cursor data-init <?php if (get_field("anchor")){ echo 'data-anchor="' . esc_attr(get_field("anchor")) . '"'; } ?>>
    <div class="contentWrapper">
        <div class="topCols">
          <div class="col">
              <?php if (get_field("title")){ ?><h1 class="hugeTitle" data-lines data-words><?php the_field("title"); ?></h1><?php } ?>
          </div>
          <div class="col">
              <?php if (get_field("top_text")){ ?><h2 class="normalTitle" data-lines data-words><?php the_field("top_text"); ?></h2><?php } ?>
          </div>
        </div>
    </div>
    <div class="bigMediaWrapper blackBackground">
        <div class="background" data-parallax data-parallax-speed="2">
            <?php if ($video): ?>
                <video poster="<?php echo esc_url($image['url']); ?>" class="video" muted playsinline loop autoplay>
                    <source src="<?php echo esc_url($video); ?>" type="video/mp4">
                </video>
            <?php elseif ($image): ?>
                <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
            <?php endif; ?>
        </div>
        <div class="contentWrapper">
            <div class="innerWrapper">
            <h3 class="subTitle white" data-lines data-words><?php the_field("subtitle"); ?></h3>
            <div class="introTextWrapper" data-anim-text>
                <div class="normalTitle white" data-lines><?php the_field("text"); ?></div>
                <div class="normalTitle white overlayText" data-lines aria-hidden="true"><?php the_field("text"); ?></div>
            </div>
            <?php if ($youtube_link): ?>
                <div class="youtubeContainer">
                    <div class="videoWrapper">
                        <iframe
                            src="https://www.youtube.com/embed/<?php echo esc_attr($youtube_link); ?>?rel=0&showinfo=0&modestbranding=1&fs=0&autoplay=0&controls=1&disablekb=0&enablejsapi=0&iv_load_policy=3&loop=0&origin=<?php echo esc_url(home_url()); ?>"
                            frameborder="0"
                            allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            allowfullscreen
                            loading="lazy"
                            title="YouTube Video"
                            referrerpolicy="strict-origin-when-cross-origin">
                        </iframe>
                    </div>
                </div>
            <?php endif; ?>
            <?php if (get_field("extra_text")){ ?>
            <div class="extraText text white">
            <p><?php the_field("extra_text"); ?></p>
            </div>
            <?php } ?>
            </div>
        </div>
    </div>
    <?php if ($image1 && $image2): ?>
    <div class="contentWrapper">
        <div class="images" data-parallax data-parallax-speed="4">
            <div class="imageWrapper">
                <div class="innerImage">
                    <img class="lazy" data-src="<?php echo esc_url($image1['sizes']['large']); ?>" alt="<?php echo esc_attr($image1['alt']); ?>" />
                </div>
            </div>
            <div class="imageWrapper" data-parallax data-parallax-speed="-2">
                <div class="innerImage">
                    <img class="lazy" data-src="<?php echo esc_url($image2['sizes']['large']); ?>" alt="<?php echo esc_attr($image2['alt']); ?>" />
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</section>
